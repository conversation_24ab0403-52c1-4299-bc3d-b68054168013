import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon

def create_gradient_rectangle():
    """
    创建一个16:9比例的矩形，具有径向渐变效果（中心深，周围浅），
    并在中间高度位置画一条横线
    """
    # 设置图形大小，保持16:9比例
    fig_width = 16
    fig_height = fig_width * 9 / 16
    fig, ax = plt.subplots(figsize=(fig_width, fig_height))
    
    # 矩形的尺寸和位置
    rect_width = 10  # 减小宽度以适应旋转后的窗口
    rect_height = rect_width * 9 / 16  # 保持16:9比例
    rect_left = fig_width / 2 - rect_width / 2  # 在窗口中心
    rect_bottom = fig_height / 2 - rect_height / 2  # 在窗口中心
    
    # 不需要预先创建网格，我们将手动计算渐变
    
    # 计算矩形中心点
    center_x = rect_left + rect_width / 2
    center_y = rect_bottom + rect_height / 2

    # 旋转角度（顺时针10度，需要转换为弧度并取负值）
    rotation_angle_degree = 10  # 负值表示顺时针
    rotation_angle_radian = rotation_angle_degree * np.pi / 180
    
    # 我们将手动创建渐变效果，不需要预先计算网格
    
    # 手动计算旋转后的矩形四个角点
    corners = np.array([
        [rect_left, rect_bottom],  # 左下角
        [rect_left + rect_width, rect_bottom],  # 右下角
        [rect_left + rect_width, rect_bottom + rect_height],  # 右上角
        [rect_left, rect_bottom + rect_height]  # 左上角
    ])

    # 旋转矩阵
    cos_angle = np.cos(rotation_angle_radian)
    sin_angle = np.sin(rotation_angle_radian)

    # 将角点平移到原点，旋转，再平移回去
    rotated_corners = []
    for corner in corners:
        # 平移到原点
        x_centered = corner[0] - center_x
        y_centered = corner[1] - center_y
        # 旋转
        x_rotated = x_centered * cos_angle - y_centered * sin_angle
        y_rotated = x_centered * sin_angle + y_centered * cos_angle
        # 平移回去
        x_final = x_rotated + center_x
        y_final = y_rotated + center_y
        rotated_corners.append([x_final, y_final])

    rotated_corners = np.array(rotated_corners)

    # 绘制旋转后的矩形边框
    # 创建一个多边形来表示旋转后的矩形
    rect_polygon = Polygon(rotated_corners, linewidth=2, edgecolor='black',
                          facecolor='none', alpha=1.0)
    ax.add_patch(rect_polygon)

    # 绘制渐变填充（使用多个小矩形来近似）
    # 创建渐变网格
    n_steps = 50
    for i in range(n_steps):
        for j in range(n_steps):
            # 计算当前小矩形在原始矩形中的位置
            x_ratio = i / n_steps
            y_ratio = j / n_steps
            x_orig = rect_left + x_ratio * rect_width
            y_orig = rect_bottom + y_ratio * rect_height

            # 计算到中心的距离
            dist_to_center = np.sqrt((x_orig - center_x)**2 + (y_orig - center_y)**2)
            max_dist = np.sqrt((rect_width/2)**2 + (rect_height/2)**2)
            normalized_dist = min(dist_to_center / max_dist, 1.0)

            # 计算旋转后的位置
            x_centered = x_orig - center_x
            y_centered = y_orig - center_y
            x_rotated = x_centered * cos_angle - y_centered * sin_angle + center_x
            y_rotated = x_centered * sin_angle + y_centered * cos_angle + center_y

            # 绘制小矩形
            step_width = rect_width / n_steps
            step_height = rect_height / n_steps
            color_intensity = 1 - normalized_dist  # 中心亮，边缘暗

            # 计算小矩形的四个角点
            small_corners = np.array([
                [x_orig - step_width/2, y_orig - step_height/2],
                [x_orig + step_width/2, y_orig - step_height/2],
                [x_orig + step_width/2, y_orig + step_height/2],
                [x_orig - step_width/2, y_orig + step_height/2]
            ])

            # 旋转小矩形的角点
            rotated_small_corners = []
            for corner in small_corners:
                x_c = corner[0] - center_x
                y_c = corner[1] - center_y
                x_r = x_c * cos_angle - y_c * sin_angle + center_x
                y_r = x_c * sin_angle + y_c * cos_angle + center_y
                rotated_small_corners.append([x_r, y_r])

            # 绘制小多边形
            small_polygon = Polygon(rotated_small_corners,
                                  facecolor=plt.cm.Blues_r(color_intensity),
                                  edgecolor='none', alpha=0.8)
            ax.add_patch(small_polygon)

    # 在矩形中间高度位置画一条横线（旋转）
    # 横线在矩形坐标系中的位置（矩形中心的水平线）
    line_start_x, line_end_x = rect_left, rect_left + rect_width
    line_y = center_y  # 矩形中心的y坐标

    # 计算旋转后的线条端点（使用与矩形相同的旋转变换）
    # 起点（矩形左边中点）
    start_x_rot = center_x + (line_start_x - center_x) * np.cos(rotation_angle_radian) - (line_y - center_y) * np.sin(rotation_angle_radian)
    start_y_rot = center_y + (line_start_x - center_x) * np.sin(rotation_angle_radian) + (line_y - center_y) * np.cos(rotation_angle_radian)
    # 终点（矩形右边中点）
    end_x_rot = center_x + (line_end_x - center_x) * np.cos(rotation_angle_radian) - (line_y - center_y) * np.sin(rotation_angle_radian)
    end_y_rot = center_y + (line_end_x - center_x) * np.sin(rotation_angle_radian) + (line_y - center_y) * np.cos(rotation_angle_radian)

    ax.plot([start_x_rot, end_x_rot], [start_y_rot, end_y_rot],
            color='red', linewidth=3, alpha=0.8)
    
    # 设置坐标轴（扩大范围以容纳旋转后的矩形）
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 9)
    ax.set_aspect('equal')
    
    # 添加标题和标签
    ax.set_title('Frame Layout', fontsize=16, pad=20)
    ax.set_xlabel('宽度', fontsize=12)
    ax.set_ylabel('高度', fontsize=12)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 显示图形
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("绘制Frame Layout...")
    create_gradient_rectangle()
