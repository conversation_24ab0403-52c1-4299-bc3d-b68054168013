import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle
import matplotlib.patches as patches
from matplotlib.transforms import Affine2D

def create_gradient_rectangle():
    """
    创建一个16:9比例的矩形，具有径向渐变效果（中心深，周围浅），
    并在中间高度位置画一条横线
    """
    # 设置图形大小，保持16:9比例
    fig, ax = plt.subplots(figsize=(16, 9))
    
    # 矩形的尺寸和位置
    rect_width = 14
    rect_height = rect_width * 9 / 16  # 保持16:9比例
    rect_x = 1
    rect_y = (9 - rect_height) / 2  # 居中放置
    
    # 创建网格用于渐变效果
    x = np.linspace(rect_x, rect_x + rect_width, 200)
    y = np.linspace(rect_y, rect_y + rect_height, 200)
    X, Y = np.meshgrid(x, y)
    
    # 计算矩形中心点
    center_x = rect_x + rect_width / 2
    center_y = rect_y + rect_height / 2

    # 旋转角度（顺时针10度，需要转换为弧度并取负值）
    rotation_angle = -10 * np.pi / 180  # 负值表示顺时针
    
    # 计算每个点到中心的距离（归一化）
    max_distance = np.sqrt((rect_width/2)**2 + (rect_height/2)**2)
    distance = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
    normalized_distance = distance / max_distance
    
    # 创建渐变效果：中心为深色(0)，边缘为浅色(1)
    gradient = normalized_distance
    
    # 绘制渐变填充（旋转）
    ax.imshow(gradient, extent=[rect_x, rect_x + rect_width, rect_y, rect_y + rect_height],
              cmap='Blues_r', alpha=0.8, aspect='auto',
              transform=ax.transData + Affine2D().rotate_deg_around(center_x, center_y, 10))
    
    # 添加矩形边框（旋转）
    rectangle = Rectangle((rect_x, rect_y), rect_width, rect_height,
                         linewidth=2, edgecolor='black', facecolor='none',
                         transform=ax.transData + Affine2D().rotate_deg_around(center_x, center_y, 10))
    ax.add_patch(rectangle)

    # 在矩形中间高度位置画一条横线（旋转）
    middle_y = center_y
    line_start_x, line_end_x = rect_x, rect_x + rect_width
    line_y = middle_y

    # 计算旋转后的线条端点
    # 起点
    start_x_rot = center_x + (line_start_x - center_x) * np.cos(rotation_angle) - (line_y - center_y) * np.sin(rotation_angle)
    start_y_rot = center_y + (line_start_x - center_x) * np.sin(rotation_angle) + (line_y - center_y) * np.cos(rotation_angle)
    # 终点
    end_x_rot = center_x + (line_end_x - center_x) * np.cos(rotation_angle) - (line_y - center_y) * np.sin(rotation_angle)
    end_y_rot = center_y + (line_end_x - center_x) * np.sin(rotation_angle) + (line_y - center_y) * np.cos(rotation_angle)

    ax.plot([start_x_rot, end_x_rot], [start_y_rot, end_y_rot],
            color='red', linewidth=3, alpha=0.8)
    
    # 设置坐标轴
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 9)
    ax.set_aspect('equal')
    
    # 添加标题和标签
    ax.set_title('16:9 矩形 - 径向渐变效果 + 中线', fontsize=16, pad=20)
    ax.set_xlabel('宽度', fontsize=12)
    ax.set_ylabel('高度', fontsize=12)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 显示图形
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("绘制Frame Layout...")
    create_gradient_rectangle()
