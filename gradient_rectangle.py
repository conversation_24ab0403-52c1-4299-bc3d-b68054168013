import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle
import matplotlib.patches as patches

def create_gradient_rectangle():
    """
    创建一个16:9比例的矩形，具有径向渐变效果（中心深，周围浅），
    并在中间高度位置画一条横线
    """
    # 设置图形大小，保持16:9比例
    fig, ax = plt.subplots(figsize=(16, 9))
    
    # 矩形的尺寸和位置
    rect_width = 14
    rect_height = rect_width * 9 / 16  # 保持16:9比例
    rect_x = 1
    rect_y = (9 - rect_height) / 2  # 居中放置
    
    # 创建网格用于渐变效果
    x = np.linspace(rect_x, rect_x + rect_width, 200)
    y = np.linspace(rect_y, rect_y + rect_height, 200)
    X, Y = np.meshgrid(x, y)
    
    # 计算矩形中心点
    center_x = rect_x + rect_width / 2
    center_y = rect_y + rect_height / 2
    
    # 计算每个点到中心的距离（归一化）
    max_distance = np.sqrt((rect_width/2)**2 + (rect_height/2)**2)
    distance = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
    normalized_distance = distance / max_distance
    
    # 创建渐变效果：中心为深色(0)，边缘为浅色(1)
    gradient = normalized_distance
    
    # 绘制渐变填充
    im = ax.imshow(gradient, extent=[rect_x, rect_x + rect_width, rect_y, rect_y + rect_height], 
                   cmap='Blues_r', alpha=0.8, aspect='auto')
    
    # 添加矩形边框
    rectangle = Rectangle((rect_x, rect_y), rect_width, rect_height, 
                         linewidth=2, edgecolor='black', facecolor='none')
    ax.add_patch(rectangle)
    
    # 在矩形中间高度位置画一条横线
    middle_y = center_y
    ax.plot([rect_x, rect_x + rect_width], [middle_y, middle_y], 
            color='red', linewidth=3, alpha=0.8)
    
    # 设置坐标轴
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 9)
    ax.set_aspect('equal')
    
    # 添加标题和标签
    ax.set_title('Frame Layout', fontsize=16, pad=20)
    ax.set_xlabel('宽度', fontsize=12)
    ax.set_ylabel('高度', fontsize=12)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 显示图形
    plt.tight_layout()
    plt.show()

def create_alternative_gradient():
    """
    另一种渐变效果的实现方式
    """
    fig, ax = plt.subplots(figsize=(16, 9))
    
    # 矩形参数
    rect_width = 14
    rect_height = rect_width * 9 / 16
    rect_x = 1
    rect_y = (9 - rect_height) / 2
    
    # 使用多个同心矩形创建渐变效果
    num_layers = 50
    colors = plt.cm.Blues_r(np.linspace(0, 1, num_layers))
    
    for i in range(num_layers):
        # 计算当前层的尺寸
        scale = (num_layers - i) / num_layers
        current_width = rect_width * scale
        current_height = rect_height * scale
        current_x = rect_x + (rect_width - current_width) / 2
        current_y = rect_y + (rect_height - current_height) / 2
        
        # 绘制当前层
        rect = Rectangle((current_x, current_y), current_width, current_height,
                        facecolor=colors[i], edgecolor='none', alpha=0.6)
        ax.add_patch(rect)
    
    # 添加边框
    border = Rectangle((rect_x, rect_y), rect_width, rect_height,
                      linewidth=2, edgecolor='black', facecolor='none')
    ax.add_patch(border)
    
    # 中间横线
    middle_y = rect_y + rect_height / 2
    ax.plot([rect_x, rect_x + rect_width], [middle_y, middle_y],
            color='red', linewidth=3, alpha=0.9)
    
    # 设置坐标轴
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 9)
    ax.set_aspect('equal')
    ax.set_title('Frame Layout', fontsize=16, pad=20)
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("绘制16:9渐变矩形...")
    print("方法1: 径向渐变")
    create_gradient_rectangle()
    
    print("\n方法2: 同心矩形渐变")
    create_alternative_gradient()
